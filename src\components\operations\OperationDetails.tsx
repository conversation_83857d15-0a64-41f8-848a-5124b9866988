
import React from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Operation, StatusCarregamentoEnum } from '@/types';
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, FileText, Info, MapPin, Package, Truck, User, Receipt, Building } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OperationDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operation: Operation | null;
  onEdit: () => void;
}

const OperationDetails = ({ open, onOpenChange, operation, onEdit }: OperationDetailsProps) => {
  if (!operation) return null;

  // Format date/time from Brazilian format
  const formatDateTime = (dateTimeString?: string) => {
    if (!dateTimeString) return '-';
    return dateTimeString;
  };

  // Get status color
  const getStatusColor = (status?: StatusCarregamentoEnum) => {
    switch (status) {
      case StatusCarregamentoEnum.CARREGADO:
        return 'bg-status-operational/10 text-status-operational';
      case StatusCarregamentoEnum.CARREGANDO:
        return 'bg-status-maintenance/10 text-status-maintenance';
      case StatusCarregamentoEnum.ATRASADO:
        return 'bg-status-warning/10 text-status-warning';
      case StatusCarregamentoEnum.NO_PRAZO:
        return 'bg-status-operational/10 text-status-operational';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span className="text-xl">Operação #{operation.chave}</span>
            <Badge className={getStatusColor(operation.statusFinalCarregamento)}>
              {operation.statusFinalCarregamento || 'Agendado'}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            Cliente: {operation.clientePagador} | {operation.idB100SvnDt}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Informações Básicas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Informações Gerais</span>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between border-b pb-2">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Motorista</span>
                  </div>
                  <span className="text-sm font-medium">{operation.motorista}</span>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Cliente</span>
                  </div>
                  <span className="text-sm font-medium">{operation.clientePagador}</span>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <div className="flex items-center gap-2">
                    <Truck className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Equipamento</span>
                  </div>
                  <span className="text-sm font-medium">{operation.modeloEquipamento}</span>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Modalidade</span>
                  </div>
                  <span className="text-sm font-medium">{operation.modalidade}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Rota</span>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Origem</span>
                  <span className="text-sm font-medium">{operation.clienteCidadeOrigem}/{operation.ufOrigem}</span>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Destino</span>
                  <span className="text-sm font-medium">{operation.clienteCidadeDestino}/{operation.ufDestino}</span>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Peso</span>
                  <span className="text-sm font-medium">{operation.peso}</span>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Dias em Rota</span>
                  <span className="text-sm font-medium">{operation.diasEmRota} dias</span>
                </div>
              </div>
            </div>
          </div>

          {/* Produto */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Produto</span>
            </div>
            <div className="p-3 bg-muted/20 rounded-md">
              <span className="text-sm font-medium">{operation.produto}</span>
            </div>
          </div>

          {/* Agendamento */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Agendamento</span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 bg-muted/20 rounded-md">
                <div className="text-xs text-muted-foreground mb-1">Carregamento</div>
                <div className="text-sm font-medium">{formatDateTime(operation.dataHoraAgendadaCarregamento)}</div>
              </div>
              <div className="p-3 bg-muted/20 rounded-md">
                <div className="text-xs text-muted-foreground mb-1">Descarga</div>
                <div className="text-sm font-medium">{formatDateTime(operation.dataHoraAgendadaDescarga)}</div>
              </div>
            </div>
          </div>

          {/* Carregamento */}
          {(operation.dataHoraChegadaCarga || operation.dataHoraInicioCarga) && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Carregamento</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {operation.dataHoraChegadaCarga && (
                  <div className="p-3 bg-muted/20 rounded-md">
                    <div className="text-xs text-muted-foreground mb-1">Chegada</div>
                    <div className="text-sm font-medium">{formatDateTime(operation.dataHoraChegadaCarga)}</div>
                  </div>
                )}
                {operation.dataHoraInicioCarga && (
                  <div className="p-3 bg-muted/20 rounded-md">
                    <div className="text-xs text-muted-foreground mb-1">Início</div>
                    <div className="text-sm font-medium">{formatDateTime(operation.dataHoraInicioCarga)}</div>
                  </div>
                )}
                {operation.dataHoraFimCarregamento && (
                  <div className="p-3 bg-muted/20 rounded-md">
                    <div className="text-xs text-muted-foreground mb-1">Fim</div>
                    <div className="text-sm font-medium">{formatDateTime(operation.dataHoraFimCarregamento)}</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Documentação */}
          {(operation.numeroNotaFiscal || operation.numeroCTE) && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Documentação</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {operation.numeroNotaFiscal && (
                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Nota Fiscal</span>
                    <span className="text-sm font-medium">{operation.numeroNotaFiscal}</span>
                  </div>
                )}
                {operation.numeroCTE && (
                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">CTE</span>
                    <span className="text-sm font-medium">{operation.numeroCTE}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Valores Financeiros */}
          {(operation.freteEmpresaNet || operation.freteTerceiro || operation.freteAgregado) && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Receipt className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Valores Financeiros</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {operation.freteEmpresaNet && (
                  <div className="p-3 bg-muted/20 rounded-md">
                    <div className="text-xs text-muted-foreground mb-1">Frete Empresa NET</div>
                    <div className="text-sm font-medium">{operation.freteEmpresaNet}</div>
                  </div>
                )}
                {operation.freteTerceiro && (
                  <div className="p-3 bg-muted/20 rounded-md">
                    <div className="text-xs text-muted-foreground mb-1">Frete Terceiro</div>
                    <div className="text-sm font-medium">{operation.freteTerceiro}</div>
                  </div>
                )}
                {operation.freteAgregado && (
                  <div className="p-3 bg-muted/20 rounded-md">
                    <div className="text-xs text-muted-foreground mb-1">Frete Agregado</div>
                    <div className="text-sm font-medium">{operation.freteAgregado}</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Observações */}
          {operation.observacao && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Observações</span>
              </div>
              <div className="p-3 bg-muted/20 rounded-md">
                <span className="text-sm">{operation.observacao}</span>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2 mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
          <Button onClick={onEdit}>
            Editar Operação
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default OperationDetails;
